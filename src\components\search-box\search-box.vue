<template>
  <div class="search-outer">
    <div ref="searchRef" :style="searchStyle" class="search" :class="{ 'is-float': isFloating }">
      <div class="search-left">
        <slot></slot>
      </div>
      <div class="search-right">
        <el-button v-if="isShowExpand" size="small" type="text" @click="showMore = !showMore">
          {{ showMore ? '收起' : '展开' }}
        </el-button>
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
const SINGLE_HEIGHT = 34;
export default {
  name: 'SearchBox',
  props: {
    // 是否默认展开
    defaultExpand: {
      type: Boolean,
      default: false
    },
    // 是否浮动模式
    isFloating: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否显示展开，收起按钮
      isShowExpand: false,
      // 展开收起标志
      showMore: false,
      // 展示是增加的高度
      addedHeight: 0,
      // 真实的内容高度（避免循环依赖）
      realScrollHeight: 0
    };
  },
  computed: {
    searchScrollHeight() {
      return this.$refs.searchRef.scrollHeight;
    },
    searchScrollWidth() {
      return this.$refs.searchRef.scrollWidth;
    },
    searchStyle() {
      return {
        height: this.showMore
          ? `${this.realScrollHeight}px`
          : `${SINGLE_HEIGHT}px`
      };
    }
  },
  watch: {
    searchScrollHeight(Val){

    },
    showMore(val) {
      if (!this.isFloating) {
        this.updateTableHeight(val);
      }
    }
  },
  methods: {
    updateTableHeight(isShowMore) {
      // 展开时，减小表格高度；收起时增加
      const addedHeight = isShowMore ? this.realScrollHeight - SINGLE_HEIGHT : 0;
      const offset = isShowMore
        ? addedHeight
        : 0 - this.addedHeight;
      this.$bus.$emit('table.updateHeight', offset);
      console.log(offset, addedHeight, this.realScrollHeight, 'addedHeight');
      this.addedHeight = addedHeight;
    },
    checkShowExpand() {
      this.isShowExpand = this.realScrollHeight > SINGLE_HEIGHT
        && this.searchScrollWidth > document.documentElement.clientWidth - 250;
    },
    // 获取真实的内容高度
    getRealScrollHeight() {
      this.$nextTick(() => {
        // 临时移除高度限制来获取真实高度
        const searchEl = this.$refs.searchRef;
        const originalHeight = searchEl.style.height;
        searchEl.style.height = 'auto';
        this.realScrollHeight = searchEl.scrollHeight;
        searchEl.style.height = originalHeight;

        // 获取真实高度后再检查是否显示展开按钮
        this.checkShowExpand();
      });
    }
  },
  mounted() {
    // 先获取真实高度，checkShowExpand 会在 getRealScrollHeight 中调用
    this.getRealScrollHeight();

    if (this.defaultExpand) {
      this.showMore = true;
    }
  }
};
</script>

<style lang="stylus" scoped>
.search-outer
  box-sizing border-box
  position relative
  padding-top 3px
  min-height 36px
  color #606266
  background-color #ffffff
  border-radius 5px
.search
  display flex
  justify-content space-between
  padding 0 8px
  background-color #ffffff
  border-radius: 6px
  height 33px
  overflow hidden
  transition height ease-in .3s

  &-left
    flex 1
  &-right
    padding-top 2px

.is-float
  box-sizing border-box
  position absolute
  left 0
  right 0
  z-index 5

</style>